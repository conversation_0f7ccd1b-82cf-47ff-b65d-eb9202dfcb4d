const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 7008;

app.use(cors());
app.use(express.json());

// Removed callYourAPI function - now using static message

app.post('/api/linkedin/messages', async (req, res) => {
    try {
        const { url } = req.body;

        if (!url) {
            return res.status(400).json({
                error: 'Profile URL is required',
                message: 'Please provide a valid LinkedIn profile URL'
            });
        }

        // Skip external API call and return static message
        console.log('Returning static message for profile:', url);
        const staticResponse = {
            messages: {
                message1: "Hello dear"
            },
            success: true
        };

        res.json(staticResponse);

    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Proxy server running on http://localhost:${PORT}`);
});
